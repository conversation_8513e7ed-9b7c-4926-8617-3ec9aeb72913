"""
واجهة إدارة العملاء المنظفة والمحسنة
تم تنظيف الكود وإزالة التكرار والأكواد غير المستخدمة
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFormLayout, QTextEdit, QDialog, QRadioButton,
                            QMenu, QAction, QFrame, QSizePolicy, QGroupBox,
                            QButtonGroup, QApplication)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QColor, QIcon
from datetime import datetime

from ui.unified_styles import UnifiedStyles, StyledTable
from database import Client, ClientPhone, update_client_balance
from utils import (show_error_message, show_info_message, show_confirmation_message, 
                   is_valid_email, is_valid_phone)


class PhoneDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل رقم هاتف"""

    def __init__(self, parent=None, phone=None):
        super().__init__(parent)
        self.phone = phone
        self.setWindowTitle("إضافة رقم هاتف" if not phone else "تعديل رقم الهاتف")
        self.setFixedSize(400, 300)
        self.init_ui()

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # النموذج
        form_layout = QFormLayout()
        
        # رقم الهاتف
        self.phone_edit = QLineEdit()
        if self.phone:
            self.phone_edit.setText(self.phone.phone_number)
        self.phone_edit.setPlaceholderText("أدخل رقم الهاتف")
        form_layout.addRow("رقم الهاتف:", self.phone_edit)
        
        # الوصف
        self.label_edit = QLineEdit()
        if self.phone and self.phone.label:
            self.label_edit.setText(self.phone.label)
        self.label_edit.setPlaceholderText("وصف الرقم (اختياري)")
        form_layout.addRow("الوصف:", self.label_edit)
        
        # رقم رئيسي
        self.is_primary = QRadioButton("رقم رئيسي")
        if self.phone and self.phone.is_primary:
            self.is_primary.setChecked(True)
        form_layout.addRow("النوع:", self.is_primary)
        
        layout.addLayout(form_layout)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.reject)
        
        save_button = QPushButton("حفظ")
        save_button.clicked.connect(self.accept)
        save_button.setDefault(True)
        
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def get_phone_data(self):
        """الحصول على بيانات الهاتف"""
        return {
            'phone_number': self.phone_edit.text().strip(),
            'label': self.label_edit.text().strip(),
            'is_primary': self.is_primary.isChecked()
        }


class BalanceDialog(QDialog):
    """نافذة حوار لتعديل رصيد العميل"""

    def __init__(self, parent=None, current_balance=0):
        super().__init__(parent)
        self.current_balance = current_balance
        self.setWindowTitle("تعديل رصيد العميل")
        self.setFixedSize(400, 250)
        self.init_ui()

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # عرض الرصيد الحالي
        current_label = QLabel(f"الرصيد الحالي: {self.current_balance:.2f}")
        current_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(current_label)
        
        # نوع العملية
        operation_group = QGroupBox("نوع العملية")
        operation_layout = QVBoxLayout()
        
        self.add_radio = QRadioButton("إضافة مبلغ")
        self.subtract_radio = QRadioButton("خصم مبلغ")
        self.set_radio = QRadioButton("تعيين رصيد جديد")
        
        self.add_radio.setChecked(True)  # افتراضي
        
        operation_layout.addWidget(self.add_radio)
        operation_layout.addWidget(self.subtract_radio)
        operation_layout.addWidget(self.set_radio)
        operation_group.setLayout(operation_layout)
        layout.addWidget(operation_group)
        
        # مجموعة أزرار الاختيار
        self.operation_group = QButtonGroup()
        self.operation_group.addButton(self.add_radio, 1)
        self.operation_group.addButton(self.subtract_radio, 2)
        self.operation_group.addButton(self.set_radio, 3)
        
        # المبلغ
        amount_layout = QHBoxLayout()
        amount_layout.addWidget(QLabel("المبلغ:"))
        self.amount_edit = QLineEdit()
        self.amount_edit.setPlaceholderText("0.00")
        amount_layout.addWidget(self.amount_edit)
        layout.addLayout(amount_layout)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.reject)
        cancel_button.setStyleSheet(UnifiedStyles.get_button_style("secondary", "normal"))
        
        save_button = QPushButton("تطبيق")
        save_button.clicked.connect(self.accept)
        save_button.setStyleSheet(UnifiedStyles.get_button_style("success", "normal"))
        
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def get_balance_data(self):
        """الحصول على بيانات الرصيد"""
        try:
            amount = float(self.amount_edit.text() or "0")
            operation = self.operation_group.checkedId()
            
            if operation == 1:  # إضافة
                new_balance = self.current_balance + amount
            elif operation == 2:  # خصم
                new_balance = self.current_balance - amount
            else:  # تعيين
                new_balance = amount
                
            return new_balance
        except ValueError:
            return None


class ClientDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل عميل"""

    def __init__(self, parent=None, client=None, session=None):
        super().__init__(parent)
        self.client = client
        self.session = session
        self.setWindowTitle("إضافة عميل جديد" if not client else "تعديل بيانات العميل")
        self.setFixedSize(500, 400)
        self.init_ui()

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # النموذج
        form_layout = QFormLayout()
        
        # الاسم
        self.name_edit = QLineEdit()
        if self.client:
            self.name_edit.setText(self.client.name)
        form_layout.addRow("الاسم:", self.name_edit)
        
        # الهاتف
        self.phone_edit = QLineEdit()
        if self.client and self.client.phones:
            primary_phone = next((p for p in self.client.phones if p.is_primary), None)
            if primary_phone:
                self.phone_edit.setText(primary_phone.phone_number)
        form_layout.addRow("الهاتف:", self.phone_edit)
        
        # الإيميل
        self.email_edit = QLineEdit()
        if self.client:
            self.email_edit.setText(self.client.email or "")
        form_layout.addRow("الإيميل:", self.email_edit)
        
        # العنوان
        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(80)
        if self.client:
            self.address_edit.setPlainText(self.client.address or "")
        form_layout.addRow("العنوان:", self.address_edit)
        
        # الملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        if self.client:
            self.notes_edit.setPlainText(self.client.notes or "")
        form_layout.addRow("الملاحظات:", self.notes_edit)
        
        layout.addLayout(form_layout)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.reject)
        
        save_button = QPushButton("حفظ")
        save_button.clicked.connect(self.save_client)
        save_button.setDefault(True)
        
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def save_client(self):
        """حفظ بيانات العميل"""
        # التحقق من صحة البيانات
        name = self.name_edit.text().strip()
        if not name:
            show_error_message("خطأ", "يجب إدخال اسم العميل")
            return
            
        phone = self.phone_edit.text().strip()
        if phone and not is_valid_phone(phone):
            show_error_message("خطأ", "رقم الهاتف غير صحيح")
            return
            
        email = self.email_edit.text().strip()
        if email and not is_valid_email(email):
            show_error_message("خطأ", "عنوان الإيميل غير صحيح")
            return
        
        try:
            if self.client:
                # تحديث العميل الموجود
                self.client.name = name
                self.client.email = email or None
                self.client.address = self.address_edit.toPlainText().strip() or None
                self.client.notes = self.notes_edit.toPlainText().strip() or None
                
                # تحديث الهاتف الرئيسي
                if phone:
                    primary_phone = next((p for p in self.client.phones if p.is_primary), None)
                    if primary_phone:
                        primary_phone.phone_number = phone
                    else:
                        new_phone = ClientPhone(
                            client_id=self.client.id,
                            phone_number=phone,
                            is_primary=True
                        )
                        self.session.add(new_phone)
            else:
                # إضافة عميل جديد
                self.client = Client(
                    name=name,
                    email=email or None,
                    address=self.address_edit.toPlainText().strip() or None,
                    notes=self.notes_edit.toPlainText().strip() or None,
                    balance=0.0,
                    created_at=datetime.now()
                )
                self.session.add(self.client)
                self.session.flush()  # للحصول على ID
                
                # إضافة الهاتف الرئيسي
                if phone:
                    new_phone = ClientPhone(
                        client_id=self.client.id,
                        phone_number=phone,
                        is_primary=True
                    )
                    self.session.add(new_phone)
            
            self.session.commit()
            self.accept()
            
        except Exception as e:
            self.session.rollback()
            show_error_message("خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")


class ClientsWidget(QWidget):
    """واجهة إدارة العملاء المنظفة"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()
        self.refresh_data()

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("🤝 إدارة العملاء")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e40af, stop:1 #3b82f6);
                border: 2px solid #1e40af;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        layout.addWidget(title_label)
        
        # شريط البحث والأزرار
        self.create_toolbar(layout)
        
        # الجدول
        self.create_table(layout)
        
        self.setLayout(layout)

    def create_toolbar(self, layout):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        # البحث
        search_label = QLabel("🔍 بحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالاسم أو الهاتف...")
        self.search_edit.textChanged.connect(self.filter_clients)
        
        toolbar_layout.addWidget(search_label)
        toolbar_layout.addWidget(self.search_edit)
        toolbar_layout.addStretch()
        
        # الأزرار
        add_button = QPushButton("➕ إضافة عميل")
        add_button.clicked.connect(self.add_client)
        
        edit_button = QPushButton("✏️ تعديل")
        edit_button.clicked.connect(self.edit_client)
        
        delete_button = QPushButton("🗑️ حذف")
        delete_button.clicked.connect(self.delete_client)
        
        balance_button = QPushButton("💰 تعديل الرصيد")
        balance_button.clicked.connect(self.edit_balance)
        
        refresh_button = QPushButton("🔄 تحديث")
        refresh_button.clicked.connect(self.refresh_data)
        
        toolbar_layout.addWidget(add_button)
        toolbar_layout.addWidget(edit_button)
        toolbar_layout.addWidget(delete_button)
        toolbar_layout.addWidget(balance_button)
        toolbar_layout.addWidget(refresh_button)
        
        layout.addWidget(toolbar_frame)

    def create_table(self, layout):
        """إنشاء الجدول"""
        styled_table = StyledTable()
        self.clients_table = styled_table.table
        
        # إعداد الأعمدة
        self.clients_table.setColumnCount(6)
        self.clients_table.setHorizontalHeaderLabels([
            "الرقم", "الاسم", "الهاتف", "الإيميل", "الرصيد", "الحالة"
        ])
        
        # إعداد خصائص الجدول
        self.clients_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.clients_table.setSelectionMode(QTableWidget.SingleSelection)
        self.clients_table.doubleClicked.connect(self.edit_client)
        
        layout.addWidget(self.clients_table)

    def refresh_data(self):
        """تحديث بيانات الجدول"""
        try:
            clients = self.session.query(Client).order_by(Client.name).all()
            self.populate_table(clients)
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")

    def populate_table(self, clients):
        """ملء الجدول بالبيانات"""
        self.clients_table.setRowCount(len(clients))

        for row, client in enumerate(clients):
            # الرقم
            self.clients_table.setItem(row, 0, QTableWidgetItem(str(client.id)))

            # الاسم
            self.clients_table.setItem(row, 1, QTableWidgetItem(client.name))

            # الهاتف
            phone_text = "غير متوفر"
            if client.phones:
                primary_phone = next((p for p in client.phones if p.is_primary), None)
                if primary_phone:
                    phone_text = primary_phone.phone_number
                elif client.phones:
                    phone_text = client.phones[0].phone_number

            phone_item = QTableWidgetItem(phone_text)
            if phone_text == "غير متوفر":
                phone_item.setForeground(QColor("red"))
            self.clients_table.setItem(row, 2, phone_item)

            # الإيميل
            email_text = client.email or "غير متوفر"
            email_item = QTableWidgetItem(email_text)
            if email_text == "غير متوفر":
                email_item.setForeground(QColor("red"))
            self.clients_table.setItem(row, 3, email_item)

            # الرصيد
            balance = client.balance or 0.0
            balance_item = QTableWidgetItem(f"{balance:.2f}")
            if balance > 0:
                balance_item.setForeground(QColor("green"))
            elif balance < 0:
                balance_item.setForeground(QColor("red"))
            self.clients_table.setItem(row, 4, balance_item)

            # الحالة
            if balance == 0:
                status_text = "✅ عادي"
                status_color = QColor("black")
            elif balance > 0:
                status_text = "💰 له مبلغ"
                status_color = QColor("green")
            else:
                status_text = "⚠️ عليه مبلغ"
                status_color = QColor("red")

            status_item = QTableWidgetItem(status_text)
            status_item.setForeground(status_color)
            self.clients_table.setItem(row, 5, status_item)

    def filter_clients(self):
        """تصفية العملاء حسب النص المدخل"""
        search_text = self.search_edit.text().strip().lower()

        if not search_text:
            self.refresh_data()
            return

        try:
            clients = self.session.query(Client).filter(
                Client.name.like(f"%{search_text}%")
            ).order_by(Client.name).all()

            # البحث في أرقام الهواتف أيضاً
            phone_clients = self.session.query(Client).join(ClientPhone).filter(
                ClientPhone.phone_number.like(f"%{search_text}%")
            ).order_by(Client.name).all()

            # دمج النتائج وإزالة التكرار
            all_clients = list(set(clients + phone_clients))
            all_clients.sort(key=lambda x: x.name)

            self.populate_table(all_clients)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء البحث: {str(e)}")

    def get_selected_client(self):
        """الحصول على العميل المحدد"""
        current_row = self.clients_table.currentRow()
        if current_row < 0:
            return None

        client_id_item = self.clients_table.item(current_row, 0)
        if not client_id_item:
            return None

        try:
            client_id = int(client_id_item.text())
            return self.session.query(Client).get(client_id)
        except (ValueError, TypeError):
            return None

    def add_client(self):
        """إضافة عميل جديد"""
        dialog = ClientDialog(self, session=self.session)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()
            show_info_message("تم", "تم إضافة العميل بنجاح")

    def edit_client(self):
        """تعديل العميل المحدد"""
        client = self.get_selected_client()
        if not client:
            show_error_message("خطأ", "يرجى تحديد عميل للتعديل")
            return

        dialog = ClientDialog(self, client=client, session=self.session)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()
            show_info_message("تم", "تم تحديث بيانات العميل بنجاح")

    def delete_client(self):
        """حذف العميل المحدد"""
        client = self.get_selected_client()
        if not client:
            show_error_message("خطأ", "يرجى تحديد عميل للحذف")
            return

        if show_confirmation_message("تأكيد الحذف",
                                   f"هل أنت متأكد من حذف العميل '{client.name}'؟"):
            try:
                self.session.delete(client)
                self.session.commit()
                self.refresh_data()
                show_info_message("تم", "تم حذف العميل بنجاح")
            except Exception as e:
                self.session.rollback()
                show_error_message("خطأ", f"حدث خطأ أثناء حذف العميل: {str(e)}")

    def edit_balance(self):
        """تعديل رصيد العميل المحدد"""
        client = self.get_selected_client()
        if not client:
            show_error_message("خطأ", "يرجى تحديد عميل لتعديل رصيده")
            return

        dialog = BalanceDialog(self, current_balance=client.balance or 0)
        if dialog.exec_() == QDialog.Accepted:
            new_balance = dialog.get_balance_data()
            if new_balance is not None:
                try:
                    update_client_balance(self.session, client.id, new_balance)
                    self.refresh_data()
                    show_info_message("تم", f"تم تحديث رصيد العميل إلى {new_balance:.2f}")
                except Exception as e:
                    show_error_message("خطأ", f"حدث خطأ أثناء تحديث الرصيد: {str(e)}")
            else:
                show_error_message("خطأ", "يرجى إدخال مبلغ صحيح")
